import 'app_logger.dart';
import 'user_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/constants.dart';

/// 用户上下文管理器 - 简化版本
class UserContext {
  static UserContext? _instance;
  String? _currentUserPhone;

  UserContext._();

  /// 获取单例实例
  static UserContext get instance {
    _instance ??= UserContext._();
    return _instance!;
  }

  /// 获取当前用户手机号
  String? get currentUserPhone => _currentUserPhone;

  /// 检查是否有用户上下文
  bool get hasUserContext =>
      _currentUserPhone != null && _currentUserPhone!.isNotEmpty;

  /// 设置当前用户
  void setCurrentUser(String? userPhone) {
    _currentUserPhone = userPhone;
    AppLogger.info('用户上下文已更新: ${userPhone ?? "已清除"}');
  }

  /// 生成用户相关的存储键名
  String getUserKey(String baseKey) {
    return UserStorage.getUserKey(baseKey, _currentUserPhone);
  }

  /// 清理当前用户数据
  Future<void> clearCurrentUserData() async {
    if (!hasUserContext) return;

    try {
      await UserStorage.clearUserData(_currentUserPhone!);
      AppLogger.info('当前用户数据已清理: $_currentUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error('清理当前用户数据失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 迁移现有数据到当前用户
  Future<void> migrateExistingDataToCurrentUser() async {
    if (!hasUserContext) return;

    try {
      await UserStorage.migrateDataToUser(_currentUserPhone!);
      AppLogger.info('数据已迁移到当前用户: $_currentUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error('数据迁移失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 切换用户
  Future<void> switchUser(String newUserPhone,
      {bool clearCurrentData = false}) async {
    final oldUserPhone = _currentUserPhone;

    try {
      // 不再清理当前用户数据，保留每个用户的数据
      // 只是简单地切换用户上下文
      setCurrentUser(newUserPhone);

      // 只在新用户没有数据时才迁移无前缀的数据
      final hasExistingData = await UserStorage.hasUserData(newUserPhone);
      if (!hasExistingData) {
        await migrateExistingDataToCurrentUser();
      }

      AppLogger.info('用户切换完成: ${oldUserPhone ?? "无"} -> $newUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error('用户切换失败: $e', error: e, stackTrace: stackTrace);
      _currentUserPhone = oldUserPhone;
      rethrow;
    }
  }

  /// 切换用户并通知 Provider 重新加载数据
  Future<void> switchUserAndReload(String newUserPhone) async {
    try {
      await switchUser(newUserPhone);

      // 通知所有相关的 Provider 重新加载数据
      // 这里可以使用事件总线或者其他机制来通知 Provider
      AppLogger.info('用户切换成功，请重新加载相关数据');
    } catch (e, stackTrace) {
      AppLogger.error('用户切换失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 检查当前用户是否有数据
  Future<bool> hasCurrentUserData() async {
    if (!hasUserContext) return false;
    return await UserStorage.hasUserData(_currentUserPhone!);
  }

  /// 重置（用于测试）
  void reset() {
    _currentUserPhone = null;
    AppLogger.info('用户上下文已重置');
  }

  /// 检查是否存在多个用户账户
  Future<bool> hasMultipleUsers() async {
    try {
      // 检查是否有多个用户的数据
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();

      // 统计不同用户的数据键
      final userPhones = <String>{};
      for (String key in allKeys) {
        // 检查是否是用户数据键格式 (phone_dataType)
        if (key.contains('_') &&
            AppConfig.userDataKeys
                .any((dataKey) => key.endsWith('_$dataKey'))) {
          final phone = key.substring(0, key.lastIndexOf('_'));
          userPhones.add(phone);
        }
      }

      return userPhones.length > 1;
    } catch (e) {
      AppLogger.error('检查多用户失败: $e', error: e);
      return false;
    }
  }

  /// 获取所有用户列表
  Future<List<String>> getAllUserPhones() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();

      final userPhones = <String>{};
      for (String key in allKeys) {
        if (key.contains('_') &&
            AppConfig.userDataKeys
                .any((dataKey) => key.endsWith('_$dataKey'))) {
          final phone = key.substring(0, key.lastIndexOf('_'));
          userPhones.add(phone);
        }
      }

      return userPhones.toList();
    } catch (e) {
      AppLogger.error('获取用户列表失败: $e', error: e);
      return [];
    }
  }
}
